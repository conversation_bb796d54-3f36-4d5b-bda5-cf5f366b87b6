<script setup lang="ts">
import type { UserSession } from '@/api/monitor'
import { <PERSON>Left, RefreshCw, Filter, Users } from 'lucide-vue-next'
import { computed, h, onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useMonitorStore } from '@/store/monitor'
import { useProjectStore } from '@/store/project'
import {
  formatTimestamp,
  formatDuration,
} from '@/utils/eventDataMapper'

// 导入新组件
import DataTable from '@/components/monitor/DataTable.vue'
import TimeRangeSelector from '@/components/monitor/TimeRangeSelector.vue'
import SessionDetailDialog from '@/components/monitor/SessionDetailDialog.vue'
import DataCard from '@/components/monitor/DataCard.vue'
import ResponsiveContainer from '@/components/monitor/ResponsiveContainer.vue'
import ResponsiveGrid from '@/components/monitor/ResponsiveGrid.vue'

const router = useRouter()
const route = useRoute()
const projectStore = useProjectStore()
const monitorStore = useMonitorStore()

// 状态
const loading = ref(false)
const showSessionDetail = ref(false)
const selectedSession = ref<UserSession | null>(null)
const currentPage = ref(1)

// 筛选条件
const filters = reactive({
  timeRange: '24h',
  userUuid: '',
  platform: '',
  customStartTime: '',
  customEndTime: '',
})

// 计算属性
const currentProject = computed(() => projectStore.currentProject)
const sessions = computed(() => monitorStore.sessions)
const pagination = computed(() => monitorStore.pagination)
const totalPages = computed(() => monitorStore.totalPages)

// 会话统计
const sessionStats = computed(() => {
  const data = sessions.value

  if (data.length === 0) {
    return {
      totalSessions: 0,
      uniqueUsers: 0,
      averageDuration: 0,
      activeSessions: 0,
      averageEvents: 0,
      errorSessions: 0,
    }
  }

  const totalSessions = pagination.value.total
  const uniqueUsers = new Set(data.map(session => session.user_uuid)).size
  const totalDuration = data.reduce((sum, session) => sum + (session.duration || 0), 0)
  const averageDuration = Math.round(totalDuration / data.length)

  // 活跃会话（最近5分钟有活动）
  const now = Date.now()
  const fiveMinutesAgo = now - 5 * 60 * 1000
  const activeSessions = data.filter(session => session.last_visit > fiveMinutesAgo).length

  // 平均事件数
  const totalEvents = data.reduce((sum, session) => sum + (session.event_count || 0), 0)
  const averageEvents = Math.round(totalEvents / data.length)

  // 有错误的会话数
  const errorSessions = data.filter(session => (session.errors || 0) > 0).length

  return {
    totalSessions,
    uniqueUsers,
    averageDuration,
    activeSessions,
    averageEvents,
    errorSessions,
  }
})

// 表格列配置
const tableColumns = computed(() => [
  {
    key: 'user_uuid',
    title: '用户',
    render: (value: string) => {
      return h('code', { class: 'text-xs' }, value?.slice(0, 8) + '...' || '未知')
    },
  },
  {
    key: 'session_id',
    title: '会话ID',
    render: (value: string) => {
      return h('code', { class: 'text-xs' }, value?.slice(0, 12) + '...' || '未知')
    },
  },
  {
    key: 'platform',
    title: '平台',
    render: (value: string) => {
      return h(Badge, { variant: 'outline' }, () => value || '未知')
    },
  },
  {
    key: 'duration',
    title: '会话时长',
    sortable: true,
    render: (value: number) => {
      return formatDuration(value || 0)
    },
  },
  {
    key: 'event_count',
    title: '事件数',
    sortable: true,
    render: (value: number) => {
      return h('span', { class: 'font-medium' }, value || 0)
    },
  },
  {
    key: 'errors',
    title: '错误数',
    render: (value: number) => {
      const errorCount = value || 0
      return h(Badge, {
        variant: errorCount > 0 ? 'destructive' : 'secondary'
      }, () => errorCount)
    },
  },
  {
    key: 'last_visit',
    title: '最后活动',
    sortable: true,
    render: (value: number) => {
      return formatTimestamp(value, 'relative')
    },
  },
])

// 表格行操作
const tableActions = [
  {
    label: '查看详情',
    action: (record: UserSession) => {
      selectedSession.value = record
      showSessionDetail.value = true
    },
  },
  {
    label: '查看事件',
    action: (record: UserSession) => {
      router.push(`/monitor/${currentProject.value?.id}/events?session=${record.session_id}`)
    },
  },
]

// 时间范围变化处理
function handleTimeRangeChange(timeRangeData: { startTime: number; endTime: number }) {
  currentPage.value = 1
  loadData(timeRangeData.startTime, timeRangeData.endTime)
}

// 加载数据
async function loadData(startTime?: number, endTime?: number) {
  if (!currentProject.value) return

  loading.value = true
  try {
    // 如果没有传入时间参数，使用默认的24小时
    const now = Date.now()
    const defaultStartTime = startTime || (now - 24 * 60 * 60 * 1000)
    const defaultEndTime = endTime || now

    const params: any = {
      project_id: currentProject.value.id,
      page: currentPage.value,
      page_size: 20,
      start_time: defaultStartTime,
      end_time: defaultEndTime,
    }

    if (filters.userUuid) params.user_uuid = filters.userUuid
    if (filters.platform) params.platform = filters.platform

    await monitorStore.fetchSessions(params)
  }
  catch (error) {
    console.error('加载会话数据失败:', error)
    toast.error('加载会话数据失败')
  }
  finally {
    loading.value = false
  }
}

// 刷新数据
function refreshData() {
  pagination.value.current_page = 1
  loadData()
}

// 时间范围变化处理
function onTimeRangeChange() {
  if (timeRange.value !== 'custom') {
    pagination.value.current_page = 1
    loadData()
  }
}

// 应用自定义时间
function applyCustomTime() {
  if (!customStartTime.value || !customEndTime.value) {
    toast.error('请选择开始和结束时间')
    return
  }

  if (new Date(customStartTime.value) >= new Date(customEndTime.value)) {
    toast.error('开始时间必须早于结束时间')
    return
  }

  pagination.value.current_page = 1
  loadData()
}

// 分页处理
function changePage(page: number) {
  pagination.value.current_page = page
  loadData()
}

// 查看会话详情
async function viewSessionDetail(session: UserSession) {
  selectedSession.value = session
  showSessionDetail.value = true

  // 加载会话相关的事件
  if (currentProject.value) {
    sessionEventsLoading.value = true
    try {
      await monitorStore.fetchSessionEvents(session.session_id, {
        project_id: currentProject.value.id,
        page: 1,
        page_size: 50,
      })
    }
    catch (error) {
      console.error('加载会话事件失败:', error)
      toast.error('加载会话事件失败')
    }
    finally {
      sessionEventsLoading.value = false
    }
  }
}

// 格式化时长
function formatDuration(milliseconds: number) {
  const minutes = Math.floor(milliseconds / 60000)
  const seconds = Math.floor((milliseconds % 60000) / 1000)
  return `${minutes}分${seconds}秒`
}

// 格式化时间
function formatDateTime(timestamp: number) {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 获取页面路径
function getPagePath(url: string) {
  try {
    return new URL(url).pathname
  }
  catch {
    return url
  }
}

// 获取事件类型对应的徽章变体
function getEventTypeVariant(eventType: string) {
  switch (eventType) {
    case 'error':
      return 'destructive'
    case 'performance':
      return 'default'
    case 'pv':
      return 'secondary'
    default:
      return 'outline'
  }
}

// 初始化项目
async function initProject() {
  const projectId = route.params.id as string
  if (projectId && (!currentProject.value || currentProject.value.id !== projectId)) {
    try {
      await projectStore.fetchProject(projectId)
    }
    catch (error) {
      console.error('获取项目信息失败:', error)
      toast.error('获取项目信息失败')
      router.push('/projects')
      return
    }
  }

  loadData()
}

// 监听路由变化
watch(() => route.params.id, initProject, { immediate: true })

// 生命周期
onMounted(() => {
  // 设置默认的自定义时间（最近24小时）
  const now = new Date()
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

  customEndTime.value = now.toISOString().slice(0, 16)
  customStartTime.value = yesterday.toISOString().slice(0, 16)
})
</script>

<template>
  <div class="session-monitor">
    <!-- 页头 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold flex items-center gap-2">
          <Users class="w-6 h-6 text-blue-500" />
          用户会话监控
        </h1>
        <p v-if="currentProject" class="text-muted-foreground mt-1">
          项目: {{ currentProject.name }}
        </p>
      </div>
      <div class="flex items-center gap-4">
        <!-- 时间范围选择器 -->
        <div class="flex items-center gap-2">
          <Label>时间范围:</Label>
          <Select v-model="timeRange" @update:model-value="onTimeRangeChange">
            <SelectTrigger class="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">
                最近1小时
              </SelectItem>
              <SelectItem value="24h">
                最近24小时
              </SelectItem>
              <SelectItem value="7d">
                最近7天
              </SelectItem>
              <SelectItem value="30d">
                最近30天
              </SelectItem>
              <SelectItem value="custom">
                自定义
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button :disabled="loading" @click="refreshData">
          <RefreshCw :class="{ 'animate-spin': loading }" class="w-4 h-4 mr-2" />
          刷新
        </Button>
      </div>
    </div>

    <!-- 自定义时间选择器 -->
    <div v-if="timeRange === 'custom'" class="mb-6 p-4 border rounded-lg">
      <div class="flex items-center gap-4">
        <div class="flex items-center gap-2">
          <Label>开始时间:</Label>
          <Input v-model="customStartTime" type="datetime-local" class="w-48" />
        </div>
        <div class="flex items-center gap-2">
          <Label>结束时间:</Label>
          <Input v-model="customEndTime" type="datetime-local" class="w-48" />
        </div>
        <Button @click="applyCustomTime">
          应用
        </Button>
      </div>
    </div>

    <!-- 会话统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            总会话数
          </CardTitle>
          <Users class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ sessions.length }}
          </div>
          <p class="text-xs text-muted-foreground">
            用户会话总数
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            平均会话时长
          </CardTitle>
          <Clock class="h-4 w-4 text-blue-500" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ averageSessionDuration }}分钟
          </div>
          <p class="text-xs text-muted-foreground">
            会话平均持续时间
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            独立用户数
          </CardTitle>
          <User class="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ uniqueUsersCount }}
          </div>
          <p class="text-xs text-muted-foreground">
            独立访问用户
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            活跃会话
          </CardTitle>
          <Activity class="h-4 w-4 text-purple-500" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ activeSessionsCount }}
          </div>
          <p class="text-xs text-muted-foreground">
            当前活跃会话数
          </p>
        </CardContent>
      </Card>
    </div>

    <!-- 会话数据表格 -->
    <Card>
      <CardHeader>
        <div class="flex justify-between items-center">
          <CardTitle>用户会话</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div v-if="sessions.length > 0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>用户ID</TableHead>
                <TableHead>会话ID</TableHead>
                <TableHead>平台</TableHead>
                <TableHead>首次访问</TableHead>
                <TableHead>最后访问</TableHead>
                <TableHead>会话时长</TableHead>
                <TableHead>页面浏览</TableHead>
                <TableHead>事件数</TableHead>
                <TableHead>错误数</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-for="session in sessions" :key="session.session_id" class="hover:bg-muted/50">
                <TableCell>
                  <code class="text-xs bg-muted px-1 py-0.5 rounded">
                    {{ session.user_uuid.slice(0, 8) }}
                  </code>
                </TableCell>
                <TableCell>
                  <code class="text-xs bg-muted px-1 py-0.5 rounded">
                    {{ session.session_id.slice(0, 8) }}
                  </code>
                </TableCell>
                <TableCell>
                  <Badge variant="secondary">
                    {{ session.platform }}
                  </Badge>
                </TableCell>
                <TableCell>
                  <span class="text-sm">{{ formatDateTime(session.first_visit) }}</span>
                </TableCell>
                <TableCell>
                  <span class="text-sm">{{ formatDateTime(session.last_visit) }}</span>
                </TableCell>
                <TableCell>
                  <span class="font-mono text-sm">{{ formatDuration(session.duration) }}</span>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {{ session.page_views }}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {{ session.event_count }}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge :variant="session.errors > 0 ? 'destructive' : 'secondary'">
                    {{ session.errors }}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Button variant="ghost" size="sm" @click="viewSessionDetail(session)">
                    查看详情
                  </Button>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>

          <!-- 分页 -->
          <div class="mt-6 flex justify-between items-center">
            <div class="text-sm text-muted-foreground">
              共 {{ pagination.total }} 条记录，第 {{ pagination.current_page }} / {{ totalPages }} 页
            </div>
            <div class="flex gap-2">
              <Button variant="outline" size="sm" :disabled="pagination.current_page <= 1" @click="changePage(pagination.current_page - 1)">
                上一页
              </Button>
              <Button variant="outline" size="sm" :disabled="pagination.current_page >= totalPages" @click="changePage(pagination.current_page + 1)">
                下一页
              </Button>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-12 text-muted-foreground">
          <Users class="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>暂无会话数据</p>
        </div>
      </CardContent>
    </Card>

    <!-- 会话详情对话框 -->
    <Dialog v-model:open="showSessionDetail">
      <DialogContent class="max-w-4xl">
        <DialogHeader>
          <DialogTitle>会话详情</DialogTitle>
        </DialogHeader>
        <div v-if="selectedSession" class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-sm font-medium">用户ID</Label>
              <p class="text-sm">
                {{ selectedSession.user_uuid }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium">会话ID</Label>
              <p class="text-sm">
                {{ selectedSession.session_id }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium">设备ID</Label>
              <p class="text-sm">
                {{ selectedSession.device_id }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium">平台</Label>
              <p class="text-sm">
                {{ selectedSession.platform }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium">首次访问</Label>
              <p class="text-sm">
                {{ formatDateTime(selectedSession.first_visit) }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium">最后访问</Label>
              <p class="text-sm">
                {{ formatDateTime(selectedSession.last_visit) }}
              </p>
            </div>
          </div>

          <!-- 会话统计 -->
          <div>
            <Label class="text-sm font-medium mb-3 block">会话统计</Label>
            <div class="grid grid-cols-4 gap-4">
              <Card>
                <CardContent class="p-4">
                  <div class="text-center">
                    <div class="text-lg font-bold">
                      {{ formatDuration(selectedSession.duration) }}
                    </div>
                    <div class="text-xs text-muted-foreground">
                      会话时长
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent class="p-4">
                  <div class="text-center">
                    <div class="text-lg font-bold">
                      {{ selectedSession.page_views }}
                    </div>
                    <div class="text-xs text-muted-foreground">
                      页面浏览数
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent class="p-4">
                  <div class="text-center">
                    <div class="text-lg font-bold">
                      {{ selectedSession.event_count }}
                    </div>
                    <div class="text-xs text-muted-foreground">
                      总事件数
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent class="p-4">
                  <div class="text-center">
                    <div class="text-lg font-bold text-red-500">
                      {{ selectedSession.errors }}
                    </div>
                    <div class="text-xs text-muted-foreground">
                      错误数
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <!-- 会话事件列表 -->
          <div>
            <Label class="text-sm font-medium mb-3 block">会话事件</Label>
            <div v-if="sessionEventsLoading" class="text-center py-4">
              <div class="inline-flex items-center gap-2">
                <div class="animate-spin w-4 h-4 border-2 border-primary border-t-transparent rounded-full" />
                <span class="text-sm">加载事件...</span>
              </div>
            </div>
            <div v-else-if="sessionEvents.length > 0" class="space-y-2 max-h-80 overflow-y-auto">
              <div v-for="event in sessionEvents" :key="event.id" class="flex items-center justify-between p-3 border rounded-lg">
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-1">
                    <Badge :variant="getEventTypeVariant(event.event_type)">
                      {{ event.event_id }}
                    </Badge>
                    <span class="text-xs text-muted-foreground">{{ formatDateTime(event.trigger_time) }}</span>
                  </div>
                  <p class="text-sm truncate">
                    {{ getPagePath(event.trigger_page_url) }}
                  </p>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8 text-muted-foreground">
              <p class="text-sm">
                暂无事件数据
              </p>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button @click="showSessionDetail = false">
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<style scoped>
.session-monitor {
  padding: 24px;
}
</style>
