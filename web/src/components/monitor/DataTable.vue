<script setup lang="ts" generic="T">
import { computed, ref } from 'vue'
import { ChevronDown, ChevronUp, MoreHorizontal, Search } from 'lucide-vue-next'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'

interface Column<T> {
  key: string
  title: string
  sortable?: boolean
  width?: string
  render?: (value: any, record: T, index: number) => any
  align?: 'left' | 'center' | 'right'
}

interface Props {
  data: T[]
  columns: Column<T>[]
  loading?: boolean
  searchable?: boolean
  searchPlaceholder?: string
  rowActions?: Array<{
    label: string
    action: (record: T) => void
    variant?: 'default' | 'destructive'
  }>
  emptyText?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  searchable: false,
  searchPlaceholder: '搜索...',
  emptyText: '暂无数据',
})

const emit = defineEmits<{
  rowClick: [record: T, index: number]
}>()

const searchQuery = ref('')
const sortColumn = ref<string>('')
const sortDirection = ref<'asc' | 'desc'>('asc')

const filteredData = computed(() => {
  let result = [...props.data]
  
  // 搜索过滤
  if (searchQuery.value && props.searchable) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(item => {
      return props.columns.some(column => {
        const value = getNestedValue(item, column.key)
        return String(value).toLowerCase().includes(query)
      })
    })
  }
  
  // 排序
  if (sortColumn.value) {
    result.sort((a, b) => {
      const aValue = getNestedValue(a, sortColumn.value)
      const bValue = getNestedValue(b, sortColumn.value)
      
      let comparison = 0
      if (aValue < bValue) comparison = -1
      else if (aValue > bValue) comparison = 1
      
      return sortDirection.value === 'desc' ? -comparison : comparison
    })
  }
  
  return result
})

function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

function handleSort(column: Column<T>) {
  if (!column.sortable) return
  
  if (sortColumn.value === column.key) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortColumn.value = column.key
    sortDirection.value = 'asc'
  }
}

function getSortIcon(column: Column<T>) {
  if (!column.sortable || sortColumn.value !== column.key) return null
  return sortDirection.value === 'asc' ? ChevronUp : ChevronDown
}

function handleRowClick(record: T, index: number) {
  emit('rowClick', record, index)
}

function renderCell(column: Column<T>, record: T, index: number) {
  const value = getNestedValue(record, column.key)
  
  if (column.render) {
    return column.render(value, record, index)
  }
  
  return value
}
</script>

<template>
  <div class="space-y-4">
    <!-- 搜索栏 -->
    <div v-if="searchable" class="flex items-center space-x-2">
      <div class="relative flex-1 max-w-sm">
        <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          v-model="searchQuery"
          :placeholder="searchPlaceholder"
          class="pl-8"
        />
      </div>
    </div>
    
    <!-- 表格 -->
    <div class="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead
              v-for="column in columns"
              :key="column.key"
              :class="[
                column.sortable ? 'cursor-pointer hover:bg-muted/50' : '',
                column.align === 'center' ? 'text-center' : '',
                column.align === 'right' ? 'text-right' : '',
              ]"
              :style="{ width: column.width }"
              @click="handleSort(column)"
            >
              <div class="flex items-center space-x-1">
                <span>{{ column.title }}</span>
                <component
                  :is="getSortIcon(column)"
                  v-if="getSortIcon(column)"
                  class="h-4 w-4"
                />
              </div>
            </TableHead>
            <TableHead v-if="rowActions && rowActions.length > 0" class="w-[50px]">
              操作
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <!-- 加载状态 -->
          <template v-if="loading">
            <TableRow v-for="i in 5" :key="i">
              <TableCell v-for="column in columns" :key="column.key">
                <Skeleton class="h-4 w-full" />
              </TableCell>
              <TableCell v-if="rowActions && rowActions.length > 0">
                <Skeleton class="h-8 w-8 rounded" />
              </TableCell>
            </TableRow>
          </template>
          
          <!-- 数据行 -->
          <template v-else-if="filteredData.length > 0">
            <TableRow
              v-for="(record, index) in filteredData"
              :key="index"
              class="cursor-pointer hover:bg-muted/50"
              @click="handleRowClick(record, index)"
            >
              <TableCell
                v-for="column in columns"
                :key="column.key"
                :class="[
                  column.align === 'center' ? 'text-center' : '',
                  column.align === 'right' ? 'text-right' : '',
                ]"
              >
                <component
                  :is="typeof renderCell(column, record, index) === 'object' ? renderCell(column, record, index) : 'span'"
                  v-if="typeof renderCell(column, record, index) === 'object'"
                />
                <span v-else>{{ renderCell(column, record, index) }}</span>
              </TableCell>
              
              <!-- 操作列 -->
              <TableCell v-if="rowActions && rowActions.length > 0">
                <DropdownMenu>
                  <DropdownMenuTrigger as-child>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal class="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      v-for="action in rowActions"
                      :key="action.label"
                      @click.stop="action.action(record)"
                    >
                      {{ action.label }}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          </template>
          
          <!-- 空状态 -->
          <TableRow v-else>
            <TableCell :colspan="columns.length + (rowActions ? 1 : 0)" class="text-center py-8">
              <div class="text-muted-foreground">{{ emptyText }}</div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  </div>
</template>
