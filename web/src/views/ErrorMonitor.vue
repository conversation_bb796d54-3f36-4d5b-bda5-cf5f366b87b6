<script setup lang="ts">
import type { MonitorEvent } from '@/api/monitor'
import { <PERSON>Left, RefreshCw, Filter } from 'lucide-vue-next'
import { computed, h, onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Pagination } from '@/components/ui/pagination'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useMonitorStore } from '@/store/monitor'
import { useProjectStore } from '@/store/project'
import {
  getErrorMessage,
  getErrorTypeName,
  getErrorSeverity,
  getErrorSeverityInfo,
  formatTimestamp,
  formatUrl,
} from '@/utils/eventDataMapper'

// 导入新组件
import DataTable from '@/components/monitor/DataTable.vue'
import TimeRangeSelector from '@/components/monitor/TimeRangeSelector.vue'
import ErrorDetailDialog from '@/components/monitor/ErrorDetailDialog.vue'
import DataCard from '@/components/monitor/DataCard.vue'

const router = useRouter()
const route = useRoute()
const projectStore = useProjectStore()
const monitorStore = useMonitorStore()

// 状态
const loading = ref(false)
const showErrorDetail = ref(false)
const selectedError = ref<MonitorEvent | null>(null)
const currentPage = ref(1)

// 筛选条件
const filters = reactive({
  timeRange: '24h',
  eventId: '',
  userUuid: '',
  pageUrl: '',
  customStartTime: '',
  customEndTime: '',
})

// 计算属性
const currentProject = computed(() => projectStore.currentProject)
const errors = computed(() => monitorStore.errors)
const pagination = computed(() => monitorStore.pagination)
const totalPages = computed(() => monitorStore.totalPages)

// 统计信息
const errorStats = computed(() => {
  const uniqueUsers = new Set(errors.value.map(e => e.user_uuid)).size
  const uniquePages = new Set(errors.value.map(e => e.trigger_page_url)).size
  const totalErrors = pagination.value.total

  // 按错误类型分组
  const errorsByType = errors.value.reduce((acc, error) => {
    const type = error.event_id
    acc[type] = (acc[type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // 按严重程度分组
  const errorsBySeverity = errors.value.reduce((acc, error) => {
    const severity = getErrorSeverity(error.event_data)
    acc[severity] = (acc[severity] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return {
    totalErrors,
    uniqueUsers,
    uniquePages,
    errorsByType,
    errorsBySeverity,
  }
})

// 表格列配置
const tableColumns = computed(() => [
  {
    key: 'event_id',
    title: '错误类型',
    sortable: true,
    render: (value: string) => {
      const severity = getErrorSeverity({ eventId: value })
      const severityInfo = getErrorSeverityInfo(severity)
      return h('div', { class: 'flex items-center gap-2' }, [
        h(Badge, {
          variant: 'outline',
          class: [severityInfo.color, severityInfo.bgColor]
        }, () => getErrorTypeName(value)),
      ])
    },
  },
  {
    key: 'event_data',
    title: '错误信息',
    render: (value: any) => {
      const message = getErrorMessage(value)
      return h('div', {
        class: 'max-w-xs truncate',
        title: message
      }, message)
    },
  },
  {
    key: 'trigger_page_url',
    title: '页面',
    render: (value: string) => {
      const urlInfo = formatUrl(value, 30)
      return h('div', {
        class: 'max-w-xs truncate',
        title: urlInfo.full
      }, urlInfo.display)
    },
  },
  {
    key: 'user_uuid',
    title: '用户',
    render: (value: string) => {
      return h('code', { class: 'text-xs' }, value?.slice(0, 8) + '...' || '未知')
    },
  },
  {
    key: 'trigger_time',
    title: '发生时间',
    sortable: true,
    render: (value: number) => {
      return formatTimestamp(value, 'relative')
    },
  },
])

// 表格行操作
const tableActions = [
  {
    label: '查看详情',
    action: (record: MonitorEvent) => {
      selectedError.value = record
      showErrorDetail.value = true
    },
  },
]

// 时间范围变化处理
function handleTimeRangeChange(timeRangeData: { startTime: number; endTime: number }) {
  currentPage.value = 1
  loadData(timeRangeData.startTime, timeRangeData.endTime)
}

// 加载数据
async function loadData(startTime?: number, endTime?: number) {
  if (!currentProject.value) return

  loading.value = true
  try {
    // 如果没有传入时间参数，使用默认的24小时
    const now = Date.now()
    const defaultStartTime = startTime || (now - 24 * 60 * 60 * 1000)
    const defaultEndTime = endTime || now

    const params: any = {
      project_id: currentProject.value.id,
      page: currentPage.value,
      page_size: 20,
      start_time: defaultStartTime,
      end_time: defaultEndTime,
    }

    if (filters.eventId) params.event_id = filters.eventId
    if (filters.userUuid) params.user_uuid = filters.userUuid
    if (filters.pageUrl) params.page_url = filters.pageUrl

    await monitorStore.fetchErrors(params)
  }
  catch (error) {
    console.error('加载错误数据失败:', error)
    toast.error('加载错误数据失败')
  }
  finally {
    loading.value = false
  }
}

// 刷新数据
function refreshData() {
  currentPage.value = 1
  loadData()
}

// 重置筛选条件
function resetFilters() {
  filters.timeRange = '24h'
  filters.eventId = ''
  filters.userUuid = ''
  filters.pageUrl = ''
  filters.customStartTime = ''
  filters.customEndTime = ''
  currentPage.value = 1
  loadData()
}

// 分页处理
function handlePageChange(page: number) {
  currentPage.value = page
  loadData()
}

// 表格行点击处理
function handleRowClick(record: MonitorEvent) {
  selectedError.value = record
  showErrorDetail.value = true
}

// 筛选条件变化处理
function handleFilterChange() {
  currentPage.value = 1
  loadData()
}

// 初始化项目
async function initProject() {
  const projectId = route.params.id as string
  if (projectId && (!currentProject.value || currentProject.value.id !== projectId)) {
    try {
      await projectStore.fetchProject(projectId)
    }
    catch (error) {
      console.error('获取项目信息失败:', error)
      toast.error('获取项目信息失败')
      router.push('/projects')
      return
    }
  }

  loadData()
}

// 监听路由变化
watch(() => route.params.id, initProject, { immediate: true })

// 生命周期
onMounted(() => {
  // 设置默认的自定义时间（最近24小时）
  const now = new Date()
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

  filters.customEndTime = now.toISOString().slice(0, 16)
  filters.customStartTime = yesterday.toISOString().slice(0, 16)
})
</script>

<template>
  <div class="error-monitor">
    <!-- 页头 -->
    <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-6">
      <div class="flex items-center gap-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="w-4 h-4 mr-2" />
          返回
        </Button>
        <div>
          <h1 class="text-2xl font-bold">错误监控</h1>
          <p v-if="currentProject" class="text-muted-foreground mt-1">
            项目: {{ currentProject.name }}
          </p>
        </div>
      </div>
      <div class="flex items-center gap-4">
        <Button :disabled="loading" size="sm" @click="refreshData">
          <RefreshCw :class="{ 'animate-spin': loading }" class="w-4 h-4 mr-2" />
          刷新
        </Button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <Card class="mb-6">
      <CardHeader>
        <CardTitle class="text-lg">
          筛选条件
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="space-y-2">
            <Label>时间范围</Label>
            <Select v-model="filters.timeRange" @update:model-value="onTimeRangeChange">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1h">
                  最近1小时
                </SelectItem>
                <SelectItem value="24h">
                  最近24小时
                </SelectItem>
                <SelectItem value="7d">
                  最近7天
                </SelectItem>
                <SelectItem value="30d">
                  最近30天
                </SelectItem>
                <SelectItem value="custom">
                  自定义
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="space-y-2">
            <Label>错误类型</Label>
            <Select v-model="filters.eventId" @update:model-value="loadData">
              <SelectTrigger>
                <SelectValue placeholder="选择错误类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="script.error">
                  脚本错误
                </SelectItem>
                <SelectItem value="resource.error">
                  资源错误
                </SelectItem>
                <SelectItem value="promise.error">
                  Promise错误
                </SelectItem>
                <SelectItem value="vue.error">
                  Vue错误
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="space-y-2">
            <Label>用户ID</Label>
            <Input
              v-model="filters.userUuid"
              placeholder="输入用户ID筛选"
              @keyup.enter="loadData"
            />
          </div>

          <div class="space-y-2">
            <Label>页面URL</Label>
            <Input
              v-model="filters.pageUrl"
              placeholder="输入页面URL筛选"
              @keyup.enter="loadData"
            />
          </div>
        </div>

        <!-- 自定义时间范围 -->
        <div v-if="filters.timeRange === 'custom'" class="mt-4 p-4 border rounded-lg">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div class="space-y-2">
              <Label>开始时间</Label>
              <Input
                v-model="filters.customStartTime"
                type="datetime-local"
              />
            </div>
            <div class="space-y-2">
              <Label>结束时间</Label>
              <Input
                v-model="filters.customEndTime"
                type="datetime-local"
              />
            </div>
            <Button @click="applyCustomTime">
              应用时间范围
            </Button>
          </div>
        </div>

        <div class="mt-4 flex gap-2">
          <Button :disabled="loading" @click="loadData">
            <Search class="w-4 h-4 mr-2" />
            搜索
          </Button>
          <Button variant="outline" @click="resetFilters">
            <RotateCcw class="w-4 h-4 mr-2" />
            重置
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 错误统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardHeader class="pb-2">
          <CardTitle class="text-sm">
            总错误数
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-red-500">
            {{ pagination.total }}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="pb-2">
          <CardTitle class="text-sm">
            影响用户数
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ uniqueUsers }}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="pb-2">
          <CardTitle class="text-sm">
            错误页面数
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ uniquePages }}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="pb-2">
          <CardTitle class="text-sm">
            错误率
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ errorRate }}%
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 错误列表 -->
    <Card>
      <CardHeader>
        <div class="flex justify-between items-center">
          <CardTitle>错误事件列表</CardTitle>
          <div class="flex items-center gap-2">
            <span class="text-sm text-muted-foreground">
              共 {{ pagination.total }} 条记录
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div v-if="loading" class="space-y-4">
          <div v-for="i in 5" :key="i" class="space-y-3">
            <Skeleton class="h-20 w-full" />
          </div>
        </div>

        <div v-else-if="errors.length > 0" class="space-y-4">
          <div
            v-for="error in errors"
            :key="error.id"
            class="border rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-2 mb-2">
                  <Badge
                    :variant="getErrorTypeVariant(error.event_id)"
                    class="text-xs"
                  >
                    {{ error.event_id }}
                  </Badge>
                  <span class="text-sm text-muted-foreground">
                    {{ formatDateTime(error.trigger_time) }}
                  </span>
                </div>

                <h3 class="font-medium text-sm mb-2">
                  {{ getErrorMessage(error.event_data) }}
                </h3>

                <div class="space-y-1 text-xs text-muted-foreground">
                  <div class="flex items-center gap-4">
                    <span>页面: {{ error.trigger_page_url }}</span>
                    <span>用户: {{ error.user_uuid }}</span>
                  </div>
                  <div v-if="hasErrorLocation(error.event_data)" class="flex items-center gap-4">
                    <span v-if="getErrorFilename(error.event_data)">文件: {{ getErrorFilename(error.event_data) }}</span>
                    <span v-if="getErrorLocation(error.event_data)">位置: {{ getErrorLocation(error.event_data) }}</span>
                  </div>
                </div>
              </div>

              <div class="flex gap-2 ml-4">
                <Button
                  variant="outline"
                  size="sm"
                  @click="viewErrorDetail(error)"
                >
                  查看详情
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  @click="viewUserSession(error)"
                >
                  用户会话
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="text-center py-12">
          <AlertTriangle class="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h3 class="text-lg font-semibold mb-2">
            暂无错误数据
          </h3>
          <p class="text-muted-foreground">
            在选定的时间范围内没有找到错误事件
          </p>
        </div>
      </CardContent>
    </Card>

    <!-- 分页 -->
    <div v-if="errors.length > 0 && totalPages > 1" class="mt-6 flex justify-center">
      <Pagination
        v-model:page="currentPage"
        :total="pagination.total"
        :items-per-page="pagination.page_size"
        :sibling-count="1"
        show-edges
        @update:page="handlePageChange"
      />
    </div>

    <!-- 错误详情对话框 -->
    <Dialog v-model:open="showErrorDetail">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>错误详情</DialogTitle>
        </DialogHeader>
        <div v-if="selectedError" class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label class="text-sm font-medium">错误类型</Label>
              <Badge :variant="getErrorTypeVariant(selectedError.event_id)">
                {{ selectedError.event_id }}
              </Badge>
            </div>
            <div class="space-y-2">
              <Label class="text-sm font-medium">发生时间</Label>
              <p class="text-sm">
                {{ formatDateTime(selectedError.trigger_time) }}
              </p>
            </div>
            <div class="space-y-2">
              <Label class="text-sm font-medium">用户ID</Label>
              <p class="text-sm font-mono">
                {{ selectedError.user_uuid }}
              </p>
            </div>
            <div class="space-y-2">
              <Label class="text-sm font-medium">会话ID</Label>
              <p class="text-sm font-mono">
                {{ selectedError.session_id }}
              </p>
            </div>
          </div>

          <!-- 页面信息 -->
          <div class="space-y-2">
            <Label class="text-sm font-medium">页面URL</Label>
            <p class="text-sm break-all bg-muted p-2 rounded">
              {{ selectedError.trigger_page_url }}
            </p>
          </div>

          <!-- 错误信息 -->
          <div class="space-y-2">
            <Label class="text-sm font-medium">错误信息</Label>
            <p class="text-sm bg-red-50 dark:bg-red-950 p-3 rounded border-l-4 border-red-500">
              {{ getErrorMessage(selectedError.event_data) }}
            </p>
          </div>

          <!-- 错误位置 -->
          <div v-if="hasErrorLocation(selectedError.event_data)" class="space-y-2">
            <Label class="text-sm font-medium">错误位置</Label>
            <div class="bg-muted p-3 rounded font-mono text-sm">
              <p v-if="getErrorFilename(selectedError.event_data)">文件: {{ getErrorFilename(selectedError.event_data) }}</p>
              <p v-if="getErrorLocation(selectedError.event_data)">位置: {{ getErrorLocation(selectedError.event_data) }}</p>
            </div>
          </div>

          <!-- 错误堆栈 -->
          <div v-if="hasErrorStack(selectedError.event_data)" class="space-y-2">
            <Label class="text-sm font-medium">错误堆栈</Label>
            <pre class="text-xs bg-muted p-3 rounded overflow-auto max-h-40 whitespace-pre-wrap">{{ getErrorStack(selectedError.event_data) }}</pre>
          </div>

          <!-- 完整数据 -->
          <div class="space-y-2">
            <Label class="text-sm font-medium">完整事件数据</Label>
            <pre class="text-xs bg-muted p-3 rounded overflow-auto max-h-40">{{ formatEventDataForDisplay(selectedError.event_data) }}</pre>
          </div>
        </div>
        <DialogFooter>
          <Button @click="showErrorDetail = false">
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<style scoped>
.error-monitor {
  padding: 24px;
}
</style>
