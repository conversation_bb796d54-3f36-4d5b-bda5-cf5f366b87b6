<script setup lang="ts">
import { computed } from 'vue'
import { 
  Eye, 
  AlertTriangle, 
  Zap, 
  <PERSON>tings, 
  MousePointer, 
  Users,
  TrendingUp,
  TrendingDown,
  Activity
} from 'lucide-vue-next'
import DataCard from './DataCard.vue'
import type { EventStatistics } from '@/api/monitor'

interface Props {
  statistics: EventStatistics | null
  loading?: boolean
  previousStatistics?: EventStatistics | null
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<{
  cardClick: [type: string]
}>()

const statsCards = computed(() => {
  const current = props.statistics
  const previous = props.previousStatistics
  
  return [
    {
      key: 'pv',
      title: '页面访问量',
      description: '用户页面访问总数',
      value: current?.pv || 0,
      icon: Eye,
      status: 'info' as const,
      trend: previous ? calculateTrend(current?.pv || 0, previous.pv || 0) : undefined,
    },
    {
      key: 'error',
      title: '错误事件',
      description: '应用错误总数',
      value: current?.error || 0,
      icon: AlertTriangle,
      status: (current?.error || 0) > 0 ? 'error' as const : 'success' as const,
      trend: previous ? calculateTrend(current?.error || 0, previous.error || 0, true) : undefined,
    },
    {
      key: 'performance',
      title: '性能监控',
      description: '性能指标记录数',
      value: current?.performance || 0,
      icon: Zap,
      status: 'warning' as const,
      trend: previous ? calculateTrend(current?.performance || 0, previous.performance || 0) : undefined,
    },
    {
      key: 'custom',
      title: '自定义事件',
      description: '自定义埋点事件',
      value: current?.custom || 0,
      icon: Settings,
      status: 'info' as const,
      trend: previous ? calculateTrend(current?.custom || 0, previous.custom || 0) : undefined,
    },
    {
      key: 'click',
      title: '用户行为',
      description: '用户点击事件',
      value: current?.click || 0,
      icon: MousePointer,
      status: 'success' as const,
      trend: previous ? calculateTrend(current?.click || 0, previous.click || 0) : undefined,
    },
  ]
})

const summaryStats = computed(() => {
  const current = props.statistics
  if (!current) return []
  
  const total = (current.pv || 0) + (current.error || 0) + (current.performance || 0) + (current.custom || 0) + (current.click || 0)
  const errorRate = total > 0 ? ((current.error || 0) / total * 100).toFixed(2) : '0.00'
  
  return [
    {
      key: 'total',
      title: '总事件数',
      description: '所有类型事件总和',
      value: total,
      icon: Activity,
      status: 'info' as const,
    },
    {
      key: 'error_rate',
      title: '错误率',
      description: '错误事件占比',
      value: errorRate,
      unit: '%',
      icon: TrendingDown,
      status: parseFloat(errorRate) > 5 ? 'error' as const : parseFloat(errorRate) > 2 ? 'warning' as const : 'success' as const,
    },
  ]
})

function calculateTrend(current: number, previous: number, inverse = false) {
  if (previous === 0) {
    return current > 0 ? { value: 100, label: '新增', direction: inverse ? 'down' as const : 'up' as const } : undefined
  }
  
  const change = ((current - previous) / previous) * 100
  const absChange = Math.abs(change)
  
  if (absChange < 1) {
    return { value: absChange, label: '持平', direction: 'neutral' as const }
  }
  
  const direction = change > 0 
    ? (inverse ? 'down' as const : 'up' as const)
    : (inverse ? 'up' as const : 'down' as const)
  
  return {
    value: parseFloat(absChange.toFixed(1)),
    label: change > 0 ? '增长' : '下降',
    direction,
  }
}

function handleCardClick(type: string) {
  emit('cardClick', type)
}
</script>

<template>
  <div class="space-y-6">
    <!-- 主要统计指标 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      <DataCard
        v-for="card in statsCards"
        :key="card.key"
        :title="card.title"
        :description="card.description"
        :value="card.value"
        :unit="card.unit"
        :trend="card.trend"
        :status="card.status"
        :icon="card.icon"
        :loading="loading"
        clickable
        @click="handleCardClick(card.key)"
      />
    </div>
    
    <!-- 汇总统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <DataCard
        v-for="card in summaryStats"
        :key="card.key"
        :title="card.title"
        :description="card.description"
        :value="card.value"
        :unit="card.unit"
        :status="card.status"
        :icon="card.icon"
        :loading="loading"
      />
      
      <!-- 占位卡片，保持布局平衡 -->
      <div class="hidden lg:block" />
      <div class="hidden lg:block" />
    </div>
  </div>
</template>
