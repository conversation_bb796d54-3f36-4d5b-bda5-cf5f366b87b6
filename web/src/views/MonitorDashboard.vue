<script setup lang="ts">
import type { MonitorEvent } from '@/api/monitor'
import {
  AlertTriangle,
  Eye,
  MousePointer,
  RefreshCw,
  Settings,
  Users,
  Zap,
} from 'lucide-vue-next'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useMonitorStore } from '@/store/monitor'
import { useProjectStore } from '@/store/project'
import {
  formatEventDataForDisplay,
  getErrorFilename,
  getErrorLocation,
  getErrorMessage,
  hasErrorLocation,
} from '@/utils/eventDataMapper'

const router = useRouter()
const route = useRoute()
const projectStore = useProjectStore()
const monitorStore = useMonitorStore()

// 状态
const loading = ref(false)
const timeRange = ref('24h')
const customStartTime = ref('')
const customEndTime = ref('')
const showErrorDetail = ref(false)
const selectedError = ref<MonitorEvent | null>(null)

// 计算属性
const currentProject = computed(() => projectStore.currentProject)
const statistics = computed(() => monitorStore.statistics)
const recentErrors = computed(() => monitorStore.errors)

// 时间范围计算
function getTimeRange() {
  const now = Date.now()
  let startTime: number
  let endTime = now

  switch (timeRange.value) {
    case '1h':
      startTime = now - 60 * 60 * 1000
      break
    case '24h':
      startTime = now - 24 * 60 * 60 * 1000
      break
    case '7d':
      startTime = now - 7 * 24 * 60 * 60 * 1000
      break
    case '30d':
      startTime = now - 30 * 24 * 60 * 60 * 1000
      break
    case 'custom':
      if (customStartTime.value && customEndTime.value) {
        startTime = new Date(customStartTime.value).getTime()
        endTime = new Date(customEndTime.value).getTime()
      }
      else {
        startTime = now - 24 * 60 * 60 * 1000
      }
      break
    default:
      startTime = now - 24 * 60 * 60 * 1000
  }

  return { startTime, endTime }
}

// 加载数据
async function loadData() {
  if (!currentProject.value)
    return

  loading.value = true
  try {
    const { startTime, endTime } = getTimeRange()

    // 并行加载统计数据和最近错误
    await Promise.all([
      monitorStore.fetchStatistics({
        project_id: currentProject.value.id,
        start_time: startTime,
        end_time: endTime,
      }),
      monitorStore.fetchErrors({
        project_id: currentProject.value.id,
        page: 1,
        page_size: 10,
        start_time: startTime,
        end_time: endTime,
      }),
    ])
  }
  catch (error) {
    console.error('加载监控数据失败:', error)
    toast.error('加载监控数据失败')
  }
  finally {
    loading.value = false
  }
}

// 刷新数据
function refreshData() {
  loadData()
}

// 时间范围变化处理
function onTimeRangeChange() {
  if (timeRange.value !== 'custom') {
    loadData()
  }
}

// 应用自定义时间
function applyCustomTime() {
  if (!customStartTime.value || !customEndTime.value) {
    toast.error('请选择开始和结束时间')
    return
  }

  if (new Date(customStartTime.value) >= new Date(customEndTime.value)) {
    toast.error('开始时间必须早于结束时间')
    return
  }

  loadData()
}

// 导航到子页面
function navigateTo(type: string) {
  if (!currentProject.value)
    return
  router.push(`/monitor/${currentProject.value.id}/${type}`)
}

// 查看错误详情
function viewErrorDetail(error: MonitorEvent) {
  selectedError.value = error
  showErrorDetail.value = true
}

// 格式化时间
function formatTime(timestamp: number) {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

function formatDateTime(timestamp: number) {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 初始化项目
async function initProject() {
  const projectId = route.params.id as string
  if (projectId && (!currentProject.value || currentProject.value.id !== projectId)) {
    try {
      await projectStore.fetchProject(projectId)
    }
    catch (error) {
      console.error('获取项目信息失败:', error)
      toast.error('获取项目信息失败')
      router.push('/projects')
      return
    }
  }

  loadData()
}

// 监听路由变化
watch(() => route.params.id, initProject, { immediate: true })

// 生命周期
onMounted(() => {
  // 设置默认的自定义时间（最近24小时）
  const now = new Date()
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

  customEndTime.value = now.toISOString().slice(0, 16)
  customStartTime.value = yesterday.toISOString().slice(0, 16)
})
</script>

<template>
  <div class="monitor-dashboard">
    <!-- 页头 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold">
          监控面板
        </h1>
        <p v-if="currentProject" class="text-muted-foreground mt-1">
          项目: {{ currentProject.name }}
        </p>
      </div>
      <div class="flex items-center gap-4">
        <!-- 时间范围选择器 -->
        <div class="flex items-center gap-2">
          <Label>时间范围:</Label>
          <Select v-model="timeRange" @update:model-value="onTimeRangeChange">
            <SelectTrigger class="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">
                最近1小时
              </SelectItem>
              <SelectItem value="24h">
                最近24小时
              </SelectItem>
              <SelectItem value="7d">
                最近7天
              </SelectItem>
              <SelectItem value="30d">
                最近30天
              </SelectItem>
              <SelectItem value="custom">
                自定义
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button :disabled="loading" @click="refreshData">
          <RefreshCw :class="{ 'animate-spin': loading }" class="w-4 h-4 mr-2" />
          刷新
        </Button>
      </div>
    </div>

    <!-- 自定义时间选择器 -->
    <div v-if="timeRange === 'custom'" class="mb-6 p-4 border rounded-lg">
      <div class="flex items-center gap-4">
        <div class="flex items-center gap-2">
          <Label>开始时间:</Label>
          <Input v-model="customStartTime" type="datetime-local" class="w-48" />
        </div>
        <div class="flex items-center gap-2">
          <Label>结束时间:</Label>
          <Input v-model="customEndTime" type="datetime-local" class="w-48" />
        </div>
        <Button @click="applyCustomTime">
          应用
        </Button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            页面访问量
          </CardTitle>
          <Eye class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ statistics?.pv || 0 }}
          </div>
          <p class="text-xs text-muted-foreground">
            用户页面访问总数
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            错误事件
          </CardTitle>
          <AlertTriangle class="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-red-500">
            {{ statistics?.error || 0 }}
          </div>
          <p class="text-xs text-muted-foreground">
            应用错误总数
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            性能监控
          </CardTitle>
          <Zap class="h-4 w-4 text-yellow-500" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ statistics?.performance || 0 }}
          </div>
          <p class="text-xs text-muted-foreground">
            性能指标记录数
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            自定义事件
          </CardTitle>
          <Settings class="h-4 w-4 text-blue-500" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ statistics?.custom || 0 }}
          </div>
          <p class="text-xs text-muted-foreground">
            自定义埋点事件
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            用户行为
          </CardTitle>
          <MousePointer class="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ statistics?.click || 0 }}
          </div>
          <p class="text-xs text-muted-foreground">
            用户点击事件
          </p>
        </CardContent>
      </Card>
    </div>

    <!-- 监控模块导航 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <Card class="cursor-pointer hover:shadow-lg transition-shadow" @click="navigateTo('errors')">
        <CardHeader class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <AlertTriangle class="w-8 h-8 text-red-500" />
          </div>
          <CardTitle>错误监控</CardTitle>
          <CardDescription>查看应用错误和异常</CardDescription>
        </CardHeader>
      </Card>

      <Card class="cursor-pointer hover:shadow-lg transition-shadow" @click="navigateTo('performance')">
        <CardHeader class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center">
            <Zap class="w-8 h-8 text-yellow-500" />
          </div>
          <CardTitle>性能监控</CardTitle>
          <CardDescription>分析页面加载性能</CardDescription>
        </CardHeader>
      </Card>

      <Card class="cursor-pointer hover:shadow-lg transition-shadow" @click="navigateTo('page-views')">
        <CardHeader class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
            <Eye class="w-8 h-8 text-blue-500" />
          </div>
          <CardTitle>页面访问</CardTitle>
          <CardDescription>统计页面访问情况</CardDescription>
        </CardHeader>
      </Card>

      <Card class="cursor-pointer hover:shadow-lg transition-shadow" @click="navigateTo('sessions')">
        <CardHeader class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
            <Users class="w-8 h-8 text-green-500" />
          </div>
          <CardTitle>用户会话</CardTitle>
          <CardDescription>分析用户会话数据</CardDescription>
        </CardHeader>
      </Card>
    </div>

    <!-- 最近错误事件 -->
    <Card class="mb-8">
      <CardHeader>
        <div class="flex justify-between items-center">
          <CardTitle>最近错误事件</CardTitle>
          <Button variant="outline" size="sm" @click="navigateTo('errors')">
            查看全部
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div v-if="recentErrors.length > 0" class="space-y-4">
          <div
            v-for="error in recentErrors.slice(0, 5)" :key="error.id"
            class="flex items-start justify-between p-4 border rounded-lg"
          >
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-2">
                <Badge variant="destructive">
                  {{ error.event_id }}
                </Badge>
                <span class="text-sm text-muted-foreground">
                  {{ formatTime(error.trigger_time) }}
                </span>
              </div>
              <p class="text-sm font-medium mb-1">
                {{ getErrorMessage(error.event_data) }}
              </p>
              <p class="text-xs text-muted-foreground">
                {{ error.trigger_page_url }}
              </p>
            </div>
            <Button variant="ghost" size="sm" @click="viewErrorDetail(error)">
              查看详情
            </Button>
          </div>
        </div>
        <div v-else class="text-center py-8 text-muted-foreground">
          暂无错误事件
        </div>
      </CardContent>
    </Card>

    <!-- 加载状态 -->
    <div v-if="loading" class="fixed inset-0 bg-black/20 flex items-center justify-center z-50">
      <div class="bg-background p-6 rounded-lg shadow-lg">
        <div class="flex items-center gap-3">
          <div class="animate-spin w-5 h-5 border-2 border-primary border-t-transparent rounded-full" />
          <span>加载中...</span>
        </div>
      </div>
    </div>

    <!-- 错误详情对话框 -->
    <Dialog v-model:open="showErrorDetail">
      <DialogContent class="max-w-2xl">
        <DialogHeader>
          <DialogTitle>错误详情</DialogTitle>
        </DialogHeader>
        <div v-if="selectedError" class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-sm font-medium">错误类型</Label>
              <p class="text-sm">
                {{ selectedError.event_id }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium">发生时间</Label>
              <p class="text-sm">
                {{ formatDateTime(selectedError.trigger_time) }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium">用户ID</Label>
              <p class="text-sm">
                {{ selectedError.user_uuid }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium">会话ID</Label>
              <p class="text-sm">
                {{ selectedError.session_id }}
              </p>
            </div>
          </div>
          <div>
            <Label class="text-sm font-medium">页面URL</Label>
            <p class="text-sm break-all">
              {{ selectedError.trigger_page_url }}
            </p>
          </div>
          <div>
            <Label class="text-sm font-medium">错误信息</Label>
            <p class="text-sm">
              {{ getErrorMessage(selectedError.event_data) }}
            </p>
          </div>
          <div v-if="hasErrorLocation(selectedError.event_data)">
            <Label class="text-sm font-medium">错误位置</Label>
            <p class="text-sm">
              <span v-if="getErrorFilename(selectedError.event_data)">{{ getErrorFilename(selectedError.event_data)
              }}</span>
              <span
                v-if="getErrorFilename(selectedError.event_data) && getErrorLocation(selectedError.event_data)"
              >:</span>
              <span v-if="getErrorLocation(selectedError.event_data)">{{ getErrorLocation(selectedError.event_data)
              }}</span>
            </p>
          </div>
          <div>
            <Label class="text-sm font-medium">完整数据</Label>
            <pre
              class="text-xs bg-muted p-3 rounded-md overflow-auto max-h-40"
            >{{ formatEventDataForDisplay(selectedError.event_data) }}</pre>
          </div>
        </div>
        <DialogFooter>
          <Button @click="showErrorDetail = false">
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<style scoped>
.monitor-dashboard {
  padding: 24px;
}
</style>
