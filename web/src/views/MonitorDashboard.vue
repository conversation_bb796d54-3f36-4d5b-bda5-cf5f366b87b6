<script setup lang="ts">
import type { MonitorEvent } from '@/api/monitor'
import { RefreshCw } from 'lucide-vue-next'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useMonitorStore } from '@/store/monitor'
import { useProjectStore } from '@/store/project'
import { getErrorMessage } from '@/utils/eventDataMapper'

// 导入新的组件
import StatsGrid from '@/components/monitor/StatsGrid.vue'
import ModuleNavigation from '@/components/monitor/ModuleNavigation.vue'
import TimeRangeSelector from '@/components/monitor/TimeRangeSelector.vue'
import ErrorDetailDialog from '@/components/monitor/ErrorDetailDialog.vue'

const router = useRouter()
const route = useRoute()
const projectStore = useProjectStore()
const monitorStore = useMonitorStore()

// 状态
const loading = ref(false)
const timeRange = ref('24h')
const customStartTime = ref('')
const customEndTime = ref('')
const showErrorDetail = ref(false)
const selectedError = ref<MonitorEvent | null>(null)

// 计算属性
const currentProject = computed(() => projectStore.currentProject)
const statistics = computed(() => monitorStore.statistics)
const recentErrors = computed(() => monitorStore.errors.slice(0, 5))

// 时间范围处理
function handleTimeRangeChange(timeRangeData: { startTime: number; endTime: number }) {
  loadData(timeRangeData.startTime, timeRangeData.endTime)
}

// 加载数据
async function loadData(startTime?: number, endTime?: number) {
  if (!currentProject.value) return

  loading.value = true
  try {
    // 如果没有传入时间参数，使用默认的24小时
    const now = Date.now()
    const defaultStartTime = startTime || (now - 24 * 60 * 60 * 1000)
    const defaultEndTime = endTime || now

    // 并行加载统计数据和最近错误
    await Promise.all([
      monitorStore.fetchStatistics({
        project_id: currentProject.value.id,
        start_time: defaultStartTime,
        end_time: defaultEndTime,
      }),
      monitorStore.fetchErrors({
        project_id: currentProject.value.id,
        page: 1,
        page_size: 10,
        start_time: defaultStartTime,
        end_time: defaultEndTime,
      }),
    ])
  }
  catch (error) {
    console.error('加载监控数据失败:', error)
    toast.error('加载监控数据失败')
  }
  finally {
    loading.value = false
  }
}

// 刷新数据
function refreshData() {
  loadData()
}

// 统计卡片点击处理
function handleStatsCardClick(type: string) {
  if (!currentProject.value) return
  router.push(`/monitor/${currentProject.value.id}/${type === 'pv' ? 'page-views' : type}`)
}

// 查看错误详情
function viewErrorDetail(error: MonitorEvent) {
  selectedError.value = error
  showErrorDetail.value = true
}

// 格式化时间
function formatTime(timestamp: number) {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

// 初始化项目
async function initProject() {
  const projectId = route.params.id as string
  if (projectId && (!currentProject.value || currentProject.value.id !== projectId)) {
    try {
      await projectStore.fetchProject(projectId)
    }
    catch (error) {
      console.error('获取项目信息失败:', error)
      toast.error('获取项目信息失败')
      router.push('/projects')
      return
    }
  }

  loadData()
}

// 监听路由变化
watch(() => route.params.id, initProject, { immediate: true })

// 生命周期
onMounted(() => {
  // 设置默认的自定义时间（最近24小时）
  const now = new Date()
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

  customEndTime.value = now.toISOString().slice(0, 16)
  customStartTime.value = yesterday.toISOString().slice(0, 16)
})
</script>

<template>
  <div class="monitor-dashboard">
    <!-- 页头 -->
    <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-6">
      <div>
        <h1 class="text-2xl font-bold">
          监控面板
        </h1>
        <p v-if="currentProject" class="text-muted-foreground mt-1">
          项目: {{ currentProject.name }}
        </p>
      </div>
      <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
        <!-- 时间范围选择器 -->
        <TimeRangeSelector
          v-model="timeRange"
          v-model:custom-start-time="customStartTime"
          v-model:custom-end-time="customEndTime"
          :disabled="loading"
          @change="handleTimeRangeChange"
        />
        <Button :disabled="loading" @click="refreshData">
          <RefreshCw :class="{ 'animate-spin': loading }" class="w-4 h-4 mr-2" />
          刷新
        </Button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <StatsGrid
      :statistics="statistics"
      :loading="loading"
      @card-click="handleStatsCardClick"
    />

    <!-- 监控模块导航 -->
    <ModuleNavigation
      v-if="currentProject"
      :project-id="currentProject.id"
      :statistics="statistics"
      class="mb-8"
    />

    <!-- 最近错误事件 -->
    <Card class="mb-8">
      <CardHeader>
        <div class="flex justify-between items-center">
          <CardTitle>最近错误事件</CardTitle>
          <Button
            variant="outline"
            size="sm"
            @click="handleStatsCardClick('errors')"
          >
            查看全部
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div v-if="recentErrors.length > 0" class="space-y-4">
          <div
            v-for="error in recentErrors"
            :key="error.id"
            class="flex items-start justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
          >
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-2">
                <Badge variant="destructive">
                  {{ error.event_id }}
                </Badge>
                <span class="text-sm text-muted-foreground">
                  {{ formatTime(error.trigger_time) }}
                </span>
              </div>
              <p class="text-sm font-medium mb-1">
                {{ getErrorMessage(error.event_data) }}
              </p>
              <p class="text-xs text-muted-foreground truncate">
                {{ error.trigger_page_url }}
              </p>
            </div>
            <Button variant="ghost" size="sm" @click="viewErrorDetail(error)">
              查看详情
            </Button>
          </div>
        </div>
        <div v-else class="text-center py-8 text-muted-foreground">
          暂无错误事件
        </div>
      </CardContent>
    </Card>

    <!-- 错误详情对话框 -->
    <ErrorDetailDialog
      v-model:open="showErrorDetail"
      :error="selectedError"
    />
  </div>
</template>

<style scoped>
.monitor-dashboard {
  padding: 1.5rem;
}

@media (min-width: 768px) {
  .monitor-dashboard {
    padding: 2rem;
  }
}
</style>
